<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        // For API requests, don't redirect - let it return 401 JSON response
        if ($request->expectsJson() || $request->is('api/*')) {
            return null;
        }

        // For web requests, return null to avoid redirect issues
        return null;
    }
}
